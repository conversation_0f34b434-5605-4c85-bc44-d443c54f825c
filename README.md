# 图片转视频工具

一个简单易用的图片转视频工具，支持拖拽操作，可以将单张图片转换为指定时长的视频文件。

## 功能特点

- 🖱️ **拖拽操作**：直接将图片文件拖拽到界面即可
- ⏱️ **自定义时长**：可设置视频时长（0.1秒-1小时）
- 🎬 **可调帧率**：支持1-60FPS帧率设置
- 📁 **灵活输出**：支持原路径输出或自定义输出目录
- 🔄 **批量处理**：支持同时处理多个图片文件
- 📂 **文件夹处理**：支持选择文件夹批量转换所有图片
- 💾 **配置保存**：自动保存用户设置（时长、帧率、输出目录等）
- 📊 **实时进度**：显示转换进度和详细处理日志
- 🖥️ **多种模式**：支持UI界面、命令行、批量处理模式
- 🌏 **中文支持**：完美支持中文路径和文件名
- 🛡️ **稳定性**：完善的错误处理和异常恢复机制

## 支持格式

**输入格式**：JPG, JPEG, PNG, BMP, TIFF, WEBP
**输出格式**：MP4

## 安装说明

### 环境要求
- Python 3.7+
- Windows/Linux/macOS

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd pic_chuli
```

2. **创建虚拟环境**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. UI界面模式（推荐）

```bash
python main.py
```

**操作步骤**：
1. 启动程序后会打开图形界面
2. 将图片文件拖拽到指定区域
3. 设置视频时长和帧率
4. 点击"开始转换"按钮
5. 等待转换完成，视频文件会保存在原图片目录

### 2. 命令行模式

```bash
# 转换单个文件
python main.py -f image.jpg -d 5.0

# 指定输出路径
python main.py -f image.jpg -d 10.0 -o output.mp4
```

### 3. 批量处理模式

```bash
# 批量处理目录中的所有图片
python main.py -b /path/to/images -d 5.0
```

### 命令行参数说明

- `-f, --file`: 输入图片文件路径
- `-b, --batch`: 输入图片目录路径（批量处理）
- `-d, --duration`: 视频时长（秒），默认5.0秒
- `-o, --output`: 输出路径（可选）
- `--version`: 显示版本信息

## 配置说明

程序会自动创建配置文件 `config/config.json`，包含以下设置：

```json
{
    "video_settings": {
        "default_duration": 5.0,    // 默认视频时长
        "fps": 30,                  // 默认帧率
        "quality": "high"           // 视频质量
    },
    "ui_settings": {
        "window_width": 800,        // 窗口宽度
        "window_height": 600,       // 窗口高度
        "theme": "default"          // 界面主题
    },
    "processing_settings": {
        "enable_logging": true,     // 启用日志
        "log_level": "INFO",        // 日志级别
        "max_concurrent_tasks": 1   // 最大并发任务数
    }
}
```

## 项目结构

```
pic_chuli/
├── core/                   # 核心功能模块
│   └── image_to_video.py  # 图片转视频核心功能
├── ui/                    # UI界面模块
│   ├── interface.py       # 界面布局
│   └── main_window.py     # 主窗口逻辑
├── config/                # 配置管理
│   ├── config.json        # 配置文件
│   └── settings.py        # 配置管理器
├── utils/                 # 工具模块
│   ├── logging_config.py  # 日志配置
│   └── thread_utils.py    # 多线程工具
├── docs/                  # 文档
├── main.py               # 主入口文件
├── requirements.txt      # 依赖列表
└── README.md            # 说明文档
```

## 开发说明

### 添加新功能
1. 在对应模块目录下创建新的Python文件
2. 更新 `docs/dependencies.md` 文档
3. 如需UI修改，在 `ui/interface.py` 中添加控件
4. 在 `ui/main_window.py` 中添加对应的信号槽处理

### 调试模式
```bash
# 启用详细日志
python main.py --log-level DEBUG
```

## 常见问题

### Q: 转换失败怎么办？
A: 检查以下几点：
- 图片文件是否损坏
- 图片格式是否支持
- 磁盘空间是否充足
- 查看日志文件获取详细错误信息

### Q: 支持哪些图片格式？
A: 目前支持 JPG, JPEG, PNG, BMP, TIFF, WEBP 格式

### Q: 视频质量如何调整？
A: 可以通过修改配置文件中的 `fps` 参数来调整视频质量

### Q: 如何批量处理大量图片？
A: 使用批量处理模式：`python main.py -b /path/to/images -d 5.0`

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的图片转视频功能
- 提供UI界面和命令行两种使用方式
- 支持拖拽操作和批量处理

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至：[<EMAIL>]
