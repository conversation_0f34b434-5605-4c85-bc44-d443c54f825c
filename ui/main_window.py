"""
主窗口类模块
管理信号槽和核心逻辑
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox, QFileDialog
from PyQt5.QtCore import QTimer, pyqtSignal, QObject

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from ui.interface import MainWindowInterface
from core.image_to_video import ImageToVideoConverter
from config.settings import ConfigManager
from utils.thread_utils import WorkerThread
from utils.logging_config import setup_logging, get_default_log_file


class ProgressSignals(QObject):
    """进度和状态信号类"""

    progress_updated = pyqtSignal(int)  # 进度更新信号
    log_message = pyqtSignal(str)  # 日志消息信号
    file_status_changed = pyqtSignal(str)  # 当前处理文件状态信号

    def __init__(self):
        super().__init__()


class LogHandler(QObject):
    """日志处理器，用于将日志输出到UI"""

    log_message = pyqtSignal(str)

    def __init__(self):
        super().__init__()

    def emit_log(self, message):
        """发送日志消息信号"""
        self.log_message.emit(message)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.converter = ImageToVideoConverter()
        self.log_handler = LogHandler()
        self.progress_signals = ProgressSignals()
        self.current_files = []
        self.current_task_thread = None
        
        # 设置日志
        self.setup_logging()
        
        # 创建界面
        self.interface = MainWindowInterface(self)
        
        # 连接信号槽
        self.connect_signals()
        
        # 加载配置
        self.load_settings()
        
        logging.info("图片转视频工具启动成功")
    
    def setup_logging(self):
        """设置日志记录"""
        log_file = get_default_log_file()
        setup_logging("INFO", log_file)
        
        # 连接日志处理器
        self.log_handler.log_message.connect(self.append_log)

        # 连接进度信号
        self.progress_signals.progress_updated.connect(self.interface.progress_bar.setValue)
        self.progress_signals.log_message.connect(self._append_log_safe)
        self.progress_signals.file_status_changed.connect(self.interface.current_file_label.setText)
    
    def connect_signals(self):
        """连接信号槽"""
        # 拖拽文件信号
        self.interface.drop_area.files_dropped.connect(self.handle_files_dropped)
        
        # 按钮信号
        self.interface.convert_button.clicked.connect(self.start_conversion)
        self.interface.clear_button.clicked.connect(self.clear_files)
        self.interface.select_folder_button.clicked.connect(self.select_folder)
        self.interface.select_files_button.clicked.connect(self.select_files)
        
        # 参数变化信号
        self.interface.duration_spinbox.valueChanged.connect(self.save_duration_setting)
        self.interface.fps_spinbox.valueChanged.connect(self.save_fps_setting)

        # 输出目录相关信号
        self.interface.use_custom_output_checkbox.toggled.connect(self.on_custom_output_toggled)
        self.interface.select_output_dir_button.clicked.connect(self.select_output_directory)
    
    def load_settings(self):
        """加载配置设置"""
        try:
            # 加载视频设置
            video_settings = self.config_manager.get_video_settings()
            self.interface.duration_spinbox.setValue(video_settings.get('default_duration', 5.0))
            self.interface.fps_spinbox.setValue(video_settings.get('fps', 30))
            
            # 加载UI设置
            ui_settings = self.config_manager.get_ui_settings()
            self.resize(ui_settings.get('window_width', 800), ui_settings.get('window_height', 600))
            
            # 设置转换器参数
            self.converter.set_fps(video_settings.get('fps', 30))

            # 加载输出目录设置
            use_custom_output = video_settings.get('use_custom_output_dir', False)
            output_dir = video_settings.get('output_dir', '')

            self.interface.use_custom_output_checkbox.setChecked(use_custom_output)
            if output_dir and os.path.exists(output_dir):
                self.interface.output_dir_label.setText(output_dir)
            else:
                self.interface.output_dir_label.setText("未选择")

            # 更新按钮状态
            self.interface.select_output_dir_button.setEnabled(use_custom_output)

            logging.info("配置加载完成")
        except Exception as e:
            logging.error(f"加载配置失败: {str(e)}")
    
    def save_duration_setting(self, value):
        """
        保存视频时长设置
        参数:
            value (float): 视频时长值
        """
        self.config_manager.set_setting('video_settings', 'default_duration', value)
    
    def save_fps_setting(self, value):
        """
        保存帧率设置
        参数:
            value (int): 帧率值
        """
        self.config_manager.set_setting('video_settings', 'fps', value)
        self.converter.set_fps(value)

    def on_custom_output_toggled(self, checked):
        """
        自定义输出目录复选框状态变化处理
        参数:
            checked (bool): 是否选中
        """
        self.interface.select_output_dir_button.setEnabled(checked)
        self.config_manager.set_setting('video_settings', 'use_custom_output_dir', checked)

        if not checked:
            self.interface.output_dir_label.setText("未选择")
            self.config_manager.set_setting('video_settings', 'output_dir', '')

    def select_output_directory(self):
        """选择输出目录"""
        current_dir = self.config_manager.get_setting('video_settings', 'output_dir', '')
        if not current_dir or not os.path.exists(current_dir):
            current_dir = os.path.expanduser('~')  # 默认用户主目录

        selected_dir = QFileDialog.getExistingDirectory(
            self,
            "选择视频输出目录",
            current_dir,
            QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
        )

        if selected_dir:
            self.interface.output_dir_label.setText(selected_dir)
            self.config_manager.set_setting('video_settings', 'output_dir', selected_dir)
            self.append_log(f"输出目录已设置为: {selected_dir}")

    def get_output_directory(self):
        """
        获取当前设置的输出目录
        返回:
            str: 输出目录路径，如果未设置或不存在则返回None
        """
        if not self.interface.use_custom_output_checkbox.isChecked():
            return None

        output_dir = self.config_manager.get_setting('video_settings', 'output_dir', '')
        if output_dir and os.path.exists(output_dir):
            return output_dir

        return None
    
    def handle_files_dropped(self, files):
        """
        处理拖拽的文件
        参数:
            files (list): 拖拽的文件路径列表
        """
        # 过滤图片文件
        image_files = []
        supported_formats = self.converter.get_supported_formats()
        
        for file_path in files:
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in supported_formats:
                image_files.append(file_path)
        
        if image_files:
            self.current_files = image_files
            self.update_file_display()
            self.append_log(f"已选择 {len(image_files)} 个图片文件")
        else:
            QMessageBox.warning(self, "警告", "未找到支持的图片文件！")
    
    def update_file_display(self):
        """更新文件显示"""
        if self.current_files:
            if len(self.current_files) == 1:
                filename = os.path.basename(self.current_files[0])
                self.interface.current_file_label.setText(f"已选择文件: {filename}")
            else:
                self.interface.current_file_label.setText(f"已选择 {len(self.current_files)} 个文件")
        else:
            self.interface.current_file_label.setText("等待选择文件...")
    
    def clear_files(self):
        """清除选择的文件"""
        self.current_files = []
        self.update_file_display()
        self.interface.progress_bar.setValue(0)
        self.append_log("已清除选择的文件")

    def select_folder(self):
        """选择文件夹并加载其中的图片文件"""
        try:
            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择包含图片的文件夹",
                os.path.expanduser('~'),
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )

            if folder_path:
                self.load_images_from_folder(folder_path)
        except Exception as e:
            error_msg = f"选择文件夹时发生错误: {str(e)}"
            self.append_log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def select_files(self):
        """选择单个或多个图片文件"""
        try:
            supported_formats = self.converter.get_supported_formats()
            # 构建文件过滤器
            format_filter = "图片文件 ("
            for fmt in supported_formats:
                format_filter += f"*{fmt} "
            format_filter = format_filter.strip() + ")"

            files, _ = QFileDialog.getOpenFileNames(
                self,
                "选择图片文件",
                os.path.expanduser('~'),
                format_filter
            )

            if files:
                self.current_files = files
                self.update_file_display()
                self.append_log(f"已选择 {len(files)} 个图片文件")
        except Exception as e:
            error_msg = f"选择文件时发生错误: {str(e)}"
            self.append_log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def load_images_from_folder(self, folder_path):
        """
        从文件夹中加载所有图片文件
        参数:
            folder_path (str): 文件夹路径
        """
        try:
            supported_formats = self.converter.get_supported_formats()
            image_files = []

            # 遍历文件夹中的所有文件
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_ext = os.path.splitext(file)[1].lower()

                    if file_ext in supported_formats:
                        image_files.append(file_path)

            if image_files:
                self.current_files = image_files
                self.update_file_display()
                self.append_log(f"从文件夹 {folder_path} 中找到 {len(image_files)} 个图片文件")
            else:
                QMessageBox.information(self, "提示", f"在文件夹 {folder_path} 中未找到支持的图片文件")

        except Exception as e:
            error_msg = f"加载文件夹中的图片时发生错误: {str(e)}"
            self.append_log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def start_conversion(self):
        """开始转换"""
        if not self.current_files:
            QMessageBox.warning(self, "警告", "请先选择图片文件！")
            return
        
        if self.current_task_thread and self.current_task_thread.is_alive():
            QMessageBox.warning(self, "警告", "转换任务正在进行中，请等待完成！")
            return
        
        # 获取设置参数
        duration = self.interface.duration_spinbox.value()
        
        # 禁用转换按钮
        self.interface.convert_button.setEnabled(False)
        self.interface.progress_bar.setValue(0)
        
        # 开始转换任务
        self.append_log("开始转换任务...")
        
        # 创建工作线程
        self.current_task_thread = WorkerThread(
            target_func=self.convert_files,
            args=(self.current_files, duration),
            callback=self.on_conversion_complete,
            error_callback=self.on_conversion_error
        )
        self.current_task_thread.start()

    def convert_files(self, files, duration):
        """
        转换文件的工作函数
        参数:
            files (list): 要转换的文件列表
            duration (float): 视频时长
        返回:
            list: 转换结果列表
        """
        results = []
        total_files = len(files)
        output_dir = self.get_output_directory()  # 获取输出目录

        for i, file_path in enumerate(files):
            try:
                # 更新当前处理文件显示
                filename = os.path.basename(file_path)
                self.progress_signals.file_status_changed.emit(f"正在处理: {filename}")
                self.progress_signals.log_message.emit(f"正在处理: {filename}")

                # 创建进度回调函数，避免闭包问题
                def create_progress_callback(file_idx, total):
                    def progress_callback(progress):
                        try:
                            # 计算总体进度
                            overall_progress = int((file_idx * 100 + progress) / total)
                            self.progress_signals.progress_updated.emit(overall_progress)
                        except Exception as e:
                            logging.error(f"更新进度时发生错误: {str(e)}")
                    return progress_callback

                # 执行转换
                success, result = self.converter.convert_image_to_video(
                    file_path,
                    duration,
                    progress_callback=create_progress_callback(i, total_files),
                    output_dir=output_dir
                )

                results.append({
                    'file': file_path,
                    'success': success,
                    'result': result
                })

                if success:
                    success_msg = f"转换成功: {filename} -> {os.path.basename(result)}"
                    self.progress_signals.log_message.emit(success_msg)
                else:
                    error_msg = f"转换失败: {filename} - {result}"
                    self.progress_signals.log_message.emit(error_msg)

            except Exception as e:
                error_msg = f"处理文件 {filename} 时发生错误: {str(e)}"
                self.progress_signals.log_message.emit(error_msg)
                results.append({
                    'file': file_path,
                    'success': False,
                    'result': error_msg
                })

        return results



    def on_conversion_complete(self, results):
        """
        转换完成回调
        参数:
            results (list): 转换结果列表
        """
        try:
            # 使用QTimer确保在主线程中执行UI更新
            QTimer.singleShot(0, lambda: self._handle_conversion_complete(results))
        except Exception as e:
            logging.error(f"转换完成回调处理失败: {str(e)}")

    def _handle_conversion_complete(self, results):
        """
        在主线程中处理转换完成
        参数:
            results (list): 转换结果列表
        """
        try:
            # 重新启用转换按钮
            self.interface.convert_button.setEnabled(True)
            self.progress_signals.progress_updated.emit(100)

            # 统计结果
            success_count = sum(1 for r in results if r['success'])
            total_count = len(results)

            # 更新状态显示
            self.progress_signals.file_status_changed.emit(f"转换完成！成功: {success_count}/{total_count}")
            self.progress_signals.log_message.emit(f"转换完成！成功: {success_count}/{total_count}")

            # 显示结果对话框
            if success_count == total_count:
                QMessageBox.information(self, "完成", f"所有文件转换成功！({success_count}/{total_count})")
            elif success_count > 0:
                QMessageBox.warning(self, "部分完成", f"部分文件转换成功！({success_count}/{total_count})")
            else:
                QMessageBox.critical(self, "失败", "所有文件转换失败！")
        except Exception as e:
            logging.error(f"处理转换完成结果时发生错误: {str(e)}")

    def on_conversion_error(self, exception):
        """
        转换错误回调
        参数:
            exception (Exception): 异常对象
        """
        try:
            # 使用QTimer确保在主线程中执行UI更新
            QTimer.singleShot(0, lambda: self._handle_conversion_error(exception))
        except Exception as e:
            logging.error(f"转换错误回调处理失败: {str(e)}")

    def _handle_conversion_error(self, exception):
        """
        在主线程中处理转换错误
        参数:
            exception (Exception): 异常对象
        """
        try:
            # 重新启用转换按钮
            self.interface.convert_button.setEnabled(True)

            error_msg = f"转换过程中发生错误: {str(exception)}"
            self.progress_signals.file_status_changed.emit("转换失败")
            self.progress_signals.log_message.emit(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
        except Exception as e:
            logging.error(f"处理转换错误时发生错误: {str(e)}")

    def append_log(self, message):
        """
        添加日志消息到UI（兼容旧接口）
        参数:
            message (str): 日志消息
        """
        self._append_log_safe(message)

    def _append_log_safe(self, message):
        """
        安全地添加日志消息到UI
        参数:
            message (str): 日志消息
        """
        try:
            # 添加时间戳
            from datetime import datetime
            timestamp = datetime.now().strftime("%H:%M:%S")
            formatted_message = f"[{timestamp}] {message}"

            self.interface.log_text.append(formatted_message)
            # 滚动到底部
            scrollbar = self.interface.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
        except Exception as e:
            logging.error(f"安全添加日志消息时发生错误: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 保存窗口大小
        size = self.size()
        self.config_manager.set_setting('ui_settings', 'window_width', size.width())
        self.config_manager.set_setting('ui_settings', 'window_height', size.height())

        # 如果有正在运行的任务，询问是否确认关闭
        if self.current_task_thread and self.current_task_thread.is_alive():
            reply = QMessageBox.question(
                self, '确认关闭',
                '转换任务正在进行中，确定要关闭程序吗？',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                event.ignore()
                return

        logging.info("程序正常退出")
        event.accept()


def handle_exception(exc_type, exc_value, exc_traceback):
    """
    全局异常处理器
    """
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    logging.critical("未捕获的异常", exc_info=(exc_type, exc_value, exc_traceback))

    # 尝试显示错误对话框
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        app = QApplication.instance()
        if app:
            QMessageBox.critical(None, "程序错误",
                               f"程序遇到未处理的错误:\n{exc_type.__name__}: {exc_value}")
    except:
        pass


def main():
    """主函数"""
    # 设置全局异常处理器
    sys.excepthook = handle_exception

    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("图片转视频工具")
    app.setApplicationVersion("1.0")

    try:
        # 创建主窗口
        window = MainWindow()
        window.show()

        # 运行应用程序
        sys.exit(app.exec_())
    except Exception as e:
        logging.critical(f"启动应用程序时发生错误: {str(e)}")
        QMessageBox.critical(None, "启动错误", f"无法启动应用程序:\n{str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
