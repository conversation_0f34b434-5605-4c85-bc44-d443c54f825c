"""
界面布局和控件初始化模块
定义主窗口的界面布局和控件
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QLineEdit, QPushButton, QProgressBar,
                             QTextEdit, QGroupBox, QSpinBox, QDoubleSpinBox,
                             QCheckBox, QFrame, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor


class DropArea(QLabel):
    """支持拖拽的区域控件"""
    
    # 定义信号
    files_dropped = pyqtSignal(list)  # 文件拖拽信号
    
    def __init__(self, parent=None):
        """初始化拖拽区域"""
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setup_ui()
    
    def setup_ui(self):
        """设置拖拽区域的UI样式"""
        self.setMinimumSize(500, 120)
        self.setMaximumHeight(120)  # 减少高度
        self.setAlignment(Qt.AlignCenter)
        self.setText("拖拽图片文件到此处\n支持格式: JPG, PNG, BMP, TIFF, WEBP")
        self.setStyleSheet("""
            QLabel {
                border: 2px dashed #bdc3c7;
                border-radius: 8px;
                background-color: #f8f9fa;
                color: #6c757d;
                font-size: 13px;
                font-weight: 500;
                padding: 15px;
                margin: 3px;
            }
            QLabel:hover {
                border-color: #007acc;
                background-color: #e3f2fd;
                color: #007acc;
            }
        """)
    
    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            self.setStyleSheet("""
                QLabel {
                    border: 2px solid #007acc;
                    border-radius: 10px;
                    background-color: #e6f3ff;
                    color: #007acc;
                    font-size: 14px;
                    padding: 20px;
                }
            """)
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.setup_ui()
    
    def dropEvent(self, event):
        """拖拽放下事件"""
        files = []
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if os.path.isfile(file_path):
                files.append(file_path)
        
        if files:
            self.files_dropped.emit(files)
        
        self.setup_ui()


class MainWindowInterface:
    """主窗口界面类"""
    
    def __init__(self, main_window):
        """
        初始化界面
        参数:
            main_window: 主窗口对象
        """
        self.main_window = main_window
        self.setup_ui()
    
    def setup_ui(self):
        """设置主窗口界面"""
        # 设置窗口属性
        self.main_window.setWindowTitle("图片转视频工具")
        self.main_window.setMinimumSize(900, 750)  # 进一步增加窗口尺寸

        # 创建中央控件
        central_widget = QWidget()
        self.main_window.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)  # 适中的组件间距
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 创建标题
        title_label = QLabel("图片转视频工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title_label.setFixedHeight(50)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                border-radius: 8px;
                padding: 8px;
            }
        """)
        main_layout.addWidget(title_label)

        # 创建拖拽区域
        self.drop_area = DropArea()
        main_layout.addWidget(self.drop_area)

        # 创建设置区域
        settings_group = self.create_settings_group()
        main_layout.addWidget(settings_group)

        # 创建控制按钮区域
        control_layout = self.create_control_buttons()
        main_layout.addLayout(control_layout)

        # 创建进度显示区域
        progress_group = self.create_progress_group()
        main_layout.addWidget(progress_group)

        # 创建日志显示区域
        log_group = self.create_log_group()
        main_layout.addWidget(log_group)
    
    def create_settings_group(self):
        """
        创建设置参数组
        返回:
            QGroupBox: 设置参数组控件
        """
        group = QGroupBox("视频设置")
        group.setFixedHeight(160)  # 固定高度
        main_layout = QVBoxLayout(group)
        main_layout.setSpacing(12)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 第一行：视频时长和帧率
        params_layout = QHBoxLayout()
        params_layout.setSpacing(25)

        # 视频时长设置
        duration_container = QVBoxLayout()
        duration_label = QLabel("视频时长(秒):")
        duration_label.setStyleSheet("font-weight: bold; color: #333;")
        self.duration_spinbox = QDoubleSpinBox()
        self.duration_spinbox.setRange(0.1, 3600.0)
        self.duration_spinbox.setValue(5.0)
        self.duration_spinbox.setDecimals(1)
        self.duration_spinbox.setSuffix(" 秒")
        self.duration_spinbox.setFixedWidth(120)
        self.duration_spinbox.setFixedHeight(28)

        duration_container.addWidget(duration_label)
        duration_container.addWidget(self.duration_spinbox)

        # 帧率设置
        fps_container = QVBoxLayout()
        fps_label = QLabel("帧率(FPS):")
        fps_label.setStyleSheet("font-weight: bold; color: #333;")
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(1, 60)
        self.fps_spinbox.setValue(30)
        self.fps_spinbox.setFixedWidth(100)
        self.fps_spinbox.setFixedHeight(28)

        fps_container.addWidget(fps_label)
        fps_container.addWidget(self.fps_spinbox)

        params_layout.addLayout(duration_container)
        params_layout.addLayout(fps_container)
        params_layout.addStretch()

        # 第二行：输出目录设置
        output_layout = QVBoxLayout()
        output_layout.setSpacing(8)

        # 输出目录复选框
        self.use_custom_output_checkbox = QCheckBox("使用自定义输出目录")
        self.use_custom_output_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                font-weight: bold;
                color: #333;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)

        # 输出目录选择行
        output_dir_layout = QHBoxLayout()
        output_dir_layout.setSpacing(10)

        # 输出目录路径显示
        self.output_dir_label = QLabel("未选择")
        self.output_dir_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-style: italic;
                background-color: #f8f8f8;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 10px;
                min-height: 16px;
            }
        """)
        self.output_dir_label.setFixedHeight(30)
        self.output_dir_label.setMinimumWidth(350)

        # 选择目录按钮
        self.select_output_dir_button = QPushButton("选择目录")
        self.select_output_dir_button.setEnabled(False)
        self.select_output_dir_button.setFixedSize(90, 30)
        self.select_output_dir_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        output_dir_layout.addWidget(self.output_dir_label)
        output_dir_layout.addWidget(self.select_output_dir_button)
        output_dir_layout.addStretch()

        output_layout.addWidget(self.use_custom_output_checkbox)
        output_layout.addLayout(output_dir_layout)

        # 添加到主布局
        main_layout.addLayout(params_layout)
        main_layout.addLayout(output_layout)

        return group
    
    def create_control_buttons(self):
        """
        创建控制按钮布局
        返回:
            QVBoxLayout: 控制按钮布局
        """
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)

        # 第一行：文件选择按钮
        file_selection_layout = QHBoxLayout()
        file_selection_layout.setSpacing(15)

        # 选择文件夹按钮
        self.select_folder_button = QPushButton("选择文件夹")
        self.select_folder_button.setFixedSize(120, 35)
        self.select_folder_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        # 选择单个文件按钮
        self.select_files_button = QPushButton("选择文件")
        self.select_files_button.setFixedSize(120, 35)
        self.select_files_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)

        file_selection_layout.addStretch()
        file_selection_layout.addWidget(self.select_folder_button)
        file_selection_layout.addWidget(self.select_files_button)
        file_selection_layout.addStretch()

        # 第二行：操作按钮
        action_layout = QHBoxLayout()
        action_layout.setSpacing(15)

        # 开始转换按钮
        self.convert_button = QPushButton("开始转换")
        self.convert_button.setFixedSize(140, 40)
        self.convert_button.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)

        # 清除按钮
        self.clear_button = QPushButton("清除")
        self.clear_button.setFixedSize(100, 40)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        action_layout.addStretch()
        action_layout.addWidget(self.convert_button)
        action_layout.addWidget(self.clear_button)
        action_layout.addStretch()

        # 添加到主布局
        main_layout.addLayout(file_selection_layout)
        main_layout.addLayout(action_layout)

        return main_layout
    
    def create_progress_group(self):
        """
        创建进度显示组
        返回:
            QGroupBox: 进度显示组控件
        """
        group = QGroupBox("转换进度")
        group.setFixedHeight(80)
        layout = QVBoxLayout(group)
        layout.setSpacing(8)
        layout.setContentsMargins(15, 15, 15, 15)

        # 当前处理文件显示
        self.current_file_label = QLabel("等待选择文件...")
        self.current_file_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                padding: 2px;
            }
        """)
        self.current_file_label.setFixedHeight(20)
        layout.addWidget(self.current_file_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFixedHeight(25)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
                font-weight: bold;
                background-color: #f8f9fa;
            }
            QProgressBar::chunk {
                background-color: #007acc;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)

        return group
    
    def create_log_group(self):
        """
        创建日志显示组
        返回:
            QGroupBox: 日志显示组控件
        """
        group = QGroupBox("处理日志")
        group.setMinimumHeight(180)
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(140)
        self.log_text.setMaximumHeight(140)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Courier New', Consolas, monospace;
                font-size: 11px;
                color: #495057;
                padding: 8px;
            }
            QTextEdit:focus {
                border-color: #007acc;
            }
        """)
        layout.addWidget(self.log_text)

        return group
