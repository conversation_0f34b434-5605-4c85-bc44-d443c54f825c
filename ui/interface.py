"""
界面布局和控件初始化模块
定义主窗口的界面布局和控件
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QLineEdit, QPushButton, QProgressBar,
                             QTextEdit, QGroupBox, QSpinBox, QDoubleSpinBox,
                             QCheckBox, QFrame, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor


class DropArea(QLabel):
    """支持拖拽的区域控件"""
    
    # 定义信号
    files_dropped = pyqtSignal(list)  # 文件拖拽信号
    
    def __init__(self, parent=None):
        """初始化拖拽区域"""
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setup_ui()
    
    def setup_ui(self):
        """设置拖拽区域的UI样式"""
        self.setMinimumSize(400, 200)
        self.setAlignment(Qt.AlignCenter)
        self.setText("拖拽图片文件到此处\n支持格式: JPG, PNG, BMP, TIFF, WEBP")
        self.setStyleSheet("""
            QLabel {
                border: 2px dashed #aaa;
                border-radius: 10px;
                background-color: #f9f9f9;
                color: #666;
                font-size: 14px;
                padding: 20px;
            }
            QLabel:hover {
                border-color: #007acc;
                background-color: #e6f3ff;
                color: #007acc;
            }
        """)
    
    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            self.setStyleSheet("""
                QLabel {
                    border: 2px solid #007acc;
                    border-radius: 10px;
                    background-color: #e6f3ff;
                    color: #007acc;
                    font-size: 14px;
                    padding: 20px;
                }
            """)
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.setup_ui()
    
    def dropEvent(self, event):
        """拖拽放下事件"""
        files = []
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if os.path.isfile(file_path):
                files.append(file_path)
        
        if files:
            self.files_dropped.emit(files)
        
        self.setup_ui()


class MainWindowInterface:
    """主窗口界面类"""
    
    def __init__(self, main_window):
        """
        初始化界面
        参数:
            main_window: 主窗口对象
        """
        self.main_window = main_window
        self.setup_ui()
    
    def setup_ui(self):
        """设置主窗口界面"""
        # 设置窗口属性
        self.main_window.setWindowTitle("图片转视频工具")
        self.main_window.setMinimumSize(800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        self.main_window.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建标题
        title_label = QLabel("图片转视频工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: #333; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建拖拽区域
        self.drop_area = DropArea()
        main_layout.addWidget(self.drop_area)
        
        # 创建设置区域
        settings_group = self.create_settings_group()
        main_layout.addWidget(settings_group)
        
        # 创建控制按钮区域
        control_layout = self.create_control_buttons()
        main_layout.addLayout(control_layout)
        
        # 创建进度显示区域
        progress_group = self.create_progress_group()
        main_layout.addWidget(progress_group)
        
        # 创建日志显示区域
        log_group = self.create_log_group()
        main_layout.addWidget(log_group)
    
    def create_settings_group(self):
        """
        创建设置参数组
        返回:
            QGroupBox: 设置参数组控件
        """
        group = QGroupBox("视频设置")
        main_layout = QVBoxLayout(group)

        # 第一行：视频时长和帧率
        first_row = QHBoxLayout()

        # 视频时长设置
        duration_label = QLabel("视频时长(秒):")
        self.duration_spinbox = QDoubleSpinBox()
        self.duration_spinbox.setRange(0.1, 3600.0)  # 0.1秒到1小时
        self.duration_spinbox.setValue(5.0)  # 默认5秒
        self.duration_spinbox.setDecimals(1)
        self.duration_spinbox.setSuffix(" 秒")

        # 帧率设置
        fps_label = QLabel("帧率(FPS):")
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(1, 60)
        self.fps_spinbox.setValue(30)  # 默认30fps

        first_row.addWidget(duration_label)
        first_row.addWidget(self.duration_spinbox)
        first_row.addWidget(fps_label)
        first_row.addWidget(self.fps_spinbox)
        first_row.addStretch()

        # 第二行：输出目录设置
        second_row = QHBoxLayout()

        # 输出目录复选框
        self.use_custom_output_checkbox = QCheckBox("使用自定义输出目录:")

        # 输出目录路径显示
        self.output_dir_label = QLabel("未选择")
        self.output_dir_label.setStyleSheet("color: #666; font-style: italic;")
        self.output_dir_label.setMinimumWidth(200)

        # 选择目录按钮
        self.select_output_dir_button = QPushButton("选择目录")
        self.select_output_dir_button.setEnabled(False)
        self.select_output_dir_button.setMaximumWidth(80)

        second_row.addWidget(self.use_custom_output_checkbox)
        second_row.addWidget(self.output_dir_label)
        second_row.addWidget(self.select_output_dir_button)
        second_row.addStretch()

        # 添加到主布局
        main_layout.addLayout(first_row)
        main_layout.addLayout(second_row)

        return group
    
    def create_control_buttons(self):
        """
        创建控制按钮布局
        返回:
            QHBoxLayout: 控制按钮布局
        """
        layout = QHBoxLayout()
        
        # 开始转换按钮
        self.convert_button = QPushButton("开始转换")
        self.convert_button.setMinimumHeight(40)
        self.convert_button.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        
        # 清除按钮
        self.clear_button = QPushButton("清除")
        self.clear_button.setMinimumHeight(40)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #666;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)
        
        layout.addStretch()
        layout.addWidget(self.convert_button)
        layout.addWidget(self.clear_button)
        layout.addStretch()
        
        return layout
    
    def create_progress_group(self):
        """
        创建进度显示组
        返回:
            QGroupBox: 进度显示组控件
        """
        group = QGroupBox("转换进度")
        layout = QVBoxLayout(group)
        
        # 当前处理文件显示
        self.current_file_label = QLabel("等待选择文件...")
        self.current_file_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(self.current_file_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        layout.addWidget(self.progress_bar)
        
        return group
    
    def create_log_group(self):
        """
        创建日志显示组
        返回:
            QGroupBox: 日志显示组控件
        """
        group = QGroupBox("处理日志")
        layout = QVBoxLayout(group)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 3px;
                font-family: Consolas, monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.log_text)
        
        return group
