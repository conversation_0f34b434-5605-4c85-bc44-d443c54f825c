#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片生成器
用于生成测试用的图片文件，包含中文路径测试
"""

import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_test_image_with_chinese_name():
    """创建包含中文名称的测试图片"""
    # 创建图片
    image = Image.new('RGB', (800, 600), color='lightblue')
    draw = ImageDraw.Draw(image)
    
    # 绘制背景渐变
    for y in range(600):
        color_value = int(255 * (1 - y / 600))
        color = (color_value, color_value + 50, 255)
        draw.line([(0, y), (800, y)], fill=color)
    
    # 绘制一些图形
    # 圆形
    draw.ellipse([50, 50, 200, 200], fill='red', outline='darkred', width=3)
    
    # 矩形
    draw.rectangle([250, 50, 450, 200], fill='green', outline='darkgreen', width=3)
    
    # 三角形
    draw.polygon([(500, 200), (600, 50), (700, 200)], fill='yellow', outline='orange', width=3)
    
    # 添加文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 40)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    text = "Chinese Path Test Image"
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    text_x = (800 - text_width) // 2
    text_y = 600 - 100
    
    # 绘制文字阴影
    draw.text((text_x + 2, text_y + 2), text, fill='black', font=font)
    # 绘制文字
    draw.text((text_x, text_y), text, fill='white', font=font)
    
    # 保存图片 - 使用中文文件名
    filename = "测试图片_中文路径.jpg"
    image.save(filename, 'JPEG', quality=95)
    print(f"测试图片已创建: {filename}")
    return filename

def create_normal_test_image():
    """创建普通英文名称的测试图片"""
    # 创建图片
    image = Image.new('RGB', (640, 480), color='lightgreen')
    draw = ImageDraw.Draw(image)
    
    # 绘制简单图案
    center_x, center_y = 320, 240
    
    # 绘制同心圆
    for i in range(5):
        radius = 30 + i * 25
        color = (255 - i * 40, 100 + i * 30, 150 + i * 20)
        draw.ellipse([
            center_x - radius, center_y - radius,
            center_x + radius, center_y + radius
        ], outline=color, width=3)
    
    # 添加文字
    try:
        font = ImageFont.truetype("arial.ttf", 30)
    except:
        font = ImageFont.load_default()
    
    draw.text((20, 20), "Normal Test Image", fill='black', font=font)
    draw.text((20, 60), "Size: 640x480", fill='black', font=font)
    
    # 保存图片
    filename = "normal_test_image.jpg"
    image.save(filename, 'JPEG', quality=95)
    print(f"测试图片已创建: {filename}")
    return filename

if __name__ == '__main__':
    print("创建测试图片...")
    
    # 创建包含中文路径的测试图片
    chinese_image = create_test_image_with_chinese_name()
    
    # 创建普通测试图片
    normal_image = create_normal_test_image()
    
    print("所有测试图片创建完成！")
    print(f"中文路径测试图片: {chinese_image}")
    print(f"普通测试图片: {normal_image}")
