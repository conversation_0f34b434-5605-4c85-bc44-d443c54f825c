"""
日志配置模块
配置应用程序的日志记录功能
"""

import logging
import os
from datetime import datetime


def setup_logging(log_level="INFO", log_file=None):
    """
    设置日志记录配置
    参数:
        log_level (str): 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file (str): 日志文件路径，如果为None则只输出到控制台
    """
    # 创建日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 配置根日志记录器
    logging.basicConfig(
        level=level,
        format=log_format,
        datefmt=date_format,
        handlers=[]
    )
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_formatter = logging.Formatter(log_format, date_format)
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(level)
            file_formatter = logging.Formatter(log_format, date_format)
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
            
            logging.info(f"日志文件已设置: {log_file}")
        except Exception as e:
            logging.error(f"无法创建日志文件: {log_file}, 错误: {str(e)}")


def get_default_log_file():
    """
    获取默认日志文件路径
    返回:
        str: 默认日志文件路径
    """
    # 获取当前脚本所在目录的上级目录
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    logs_dir = os.path.join(current_dir, 'logs')
    
    # 生成带时间戳的日志文件名
    timestamp = datetime.now().strftime('%Y%m%d')
    log_filename = f'pic_to_video_{timestamp}.log'
    
    return os.path.join(logs_dir, log_filename)
