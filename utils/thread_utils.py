"""
多线程工具模块
提供多线程处理功能，避免UI界面阻塞
"""

import threading
import queue
import logging
from typing import Callable, Any


class WorkerThread(threading.Thread):
    """工作线程类，用于在后台执行任务"""
    
    def __init__(self, target_func, args=(), kwargs=None, callback=None, error_callback=None):
        """
        初始化工作线程
        参数:
            target_func (Callable): 要执行的目标函数
            args (tuple): 传递给目标函数的位置参数
            kwargs (dict): 传递给目标函数的关键字参数
            callback (Callable): 成功完成时的回调函数，接收结果作为参数
            error_callback (Callable): 发生错误时的回调函数，接收异常作为参数
        """
        super().__init__()
        self.target_func = target_func
        self.args = args
        self.kwargs = kwargs or {}
        self.callback = callback
        self.error_callback = error_callback
        self.result = None
        self.exception = None
        self.daemon = True  # 设置为守护线程
    
    def run(self):
        """执行线程任务"""
        try:
            logging.info(f"开始执行后台任务: {self.target_func.__name__}")
            self.result = self.target_func(*self.args, **self.kwargs)
            
            if self.callback:
                self.callback(self.result)
                
            logging.info(f"后台任务完成: {self.target_func.__name__}")
            
        except Exception as e:
            self.exception = e
            logging.error(f"后台任务执行失败: {self.target_func.__name__}, 错误: {str(e)}")
            
            if self.error_callback:
                self.error_callback(e)


class TaskQueue:
    """任务队列类，用于管理多个任务的执行"""
    
    def __init__(self, max_workers=1):
        """
        初始化任务队列
        参数:
            max_workers (int): 最大工作线程数
        """
        self.max_workers = max_workers
        self.task_queue = queue.Queue()
        self.workers = []
        self.is_running = False
    
    def add_task(self, target_func, args=(), kwargs=None, callback=None, error_callback=None):
        """
        添加任务到队列
        参数:
            target_func (Callable): 要执行的目标函数
            args (tuple): 传递给目标函数的位置参数
            kwargs (dict): 传递给目标函数的关键字参数
            callback (Callable): 成功完成时的回调函数
            error_callback (Callable): 发生错误时的回调函数
        """
        task = {
            'target_func': target_func,
            'args': args,
            'kwargs': kwargs or {},
            'callback': callback,
            'error_callback': error_callback
        }
        self.task_queue.put(task)
        logging.info(f"任务已添加到队列: {target_func.__name__}")
    
    def start(self):
        """启动任务队列处理"""
        if self.is_running:
            logging.warning("任务队列已在运行中")
            return
        
        self.is_running = True
        
        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker, daemon=True)
            worker.start()
            self.workers.append(worker)
        
        logging.info(f"任务队列已启动，工作线程数: {self.max_workers}")
    
    def stop(self):
        """停止任务队列处理"""
        self.is_running = False
        
        # 向队列添加停止信号
        for _ in range(self.max_workers):
            self.task_queue.put(None)
        
        # 等待所有工作线程结束
        for worker in self.workers:
            worker.join()
        
        self.workers.clear()
        logging.info("任务队列已停止")
    
    def _worker(self):
        """工作线程的主循环"""
        while self.is_running:
            try:
                task = self.task_queue.get(timeout=1)
                
                if task is None:  # 停止信号
                    break
                
                # 执行任务
                worker_thread = WorkerThread(
                    target_func=task['target_func'],
                    args=task['args'],
                    kwargs=task['kwargs'],
                    callback=task['callback'],
                    error_callback=task['error_callback']
                )
                worker_thread.start()
                worker_thread.join()
                
                self.task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"工作线程执行任务时发生错误: {str(e)}")
    
    def get_queue_size(self):
        """
        获取队列中待处理任务数量
        返回:
            int: 待处理任务数量
        """
        return self.task_queue.qsize()
