# 项目依赖关系文档

## 项目结构

```
pic_chuli/                         # 项目根目录
├── core/                          # 核心功能模块
│   ├── __init__.py               # 核心模块包初始化
│   └── image_to_video.py         # 图片转视频核心功能
├── ui/                           # UI界面模块
│   ├── __init__.py               # UI模块包初始化
│   ├── interface.py              # 界面布局和控件初始化
│   └── main_window.py            # 主窗口类，管理信号槽和核心逻辑
├── config/                       # 配置管理模块
│   ├── __init__.py               # 配置模块包初始化
│   ├── config.json               # 默认配置文件
│   └── settings.py               # 配置文件读取和保存功能
├── utils/                        # 工具模块
│   ├── __init__.py               # 工具模块包初始化
│   ├── logging_config.py         # 日志配置工具
│   └── thread_utils.py           # 多线程处理工具
├── docs/                         # 文档目录
│   ├── dependencies.md           # 本文件，依赖关系文档
│   └── architecture.md           # 项目架构说明
├── venv/                         # 虚拟环境目录
├── main.py                       # 主入口文件
├── requirements.txt              # 依赖包列表
└── README.md                     # 项目说明文档
```

## 文件依赖关系

### `main.py`:
- 调用 `ui/main_window.py` 的 `main()` 函数（UI模式）
- 调用 `core/image_to_video.py` 的 `ImageToVideoConverter` 类（命令行模式）
- 调用 `config/settings.py` 的 `ConfigManager` 类
- 调用 `utils/logging_config.py` 的日志配置函数

### `ui/main_window.py`:
- 调用 `ui/interface.py` 的 `MainWindowInterface` 类
- 调用 `core/image_to_video.py` 的 `ImageToVideoConverter` 类
- 调用 `config/settings.py` 的 `ConfigManager` 类
- 调用 `utils/thread_utils.py` 的 `WorkerThread` 类
- 调用 `utils/logging_config.py` 的日志配置函数

### `ui/interface.py`:
- 无外部项目依赖，仅依赖PyQt5库

### `core/image_to_video.py`:
- 无外部项目依赖，仅依赖opencv-python、Pillow、numpy库

### `config/settings.py`:
- 无外部项目依赖，仅依赖标准库json、os、logging

### `utils/logging_config.py`:
- 无外部项目依赖，仅依赖标准库logging、os、datetime

### `utils/thread_utils.py`:
- 无外部项目依赖，仅依赖标准库threading、queue、logging

## 功能执行顺序

### UI模式图片转视频执行流程：

1. **程序启动**：
   - `main.py` → `ui/main_window.py` → `main()`

2. **界面初始化**：
   - `ui/main_window.py` → `MainWindow.__init__()`
   - `MainWindow.__init__()` → `config/settings.py` → `ConfigManager()`
   - `MainWindow.__init__()` → `core/image_to_video.py` → `ImageToVideoConverter()`
   - `MainWindow.__init__()` → `ui/interface.py` → `MainWindowInterface()`
   - `MainWindow.__init__()` → `utils/logging_config.py` → `setup_logging()`

3. **用户拖拽文件**：
   - `ui/interface.py` → `DropArea.dropEvent()` → 发送 `files_dropped` 信号
   - `ui/main_window.py` → `MainWindow.handle_files_dropped()` → 处理文件列表

4. **开始转换**：
   - `ui/main_window.py` → `MainWindow.start_conversion()`
   - `MainWindow.start_conversion()` → `utils/thread_utils.py` → `WorkerThread()`
   - `WorkerThread` → `MainWindow.convert_files()`
   - `MainWindow.convert_files()` → `core/image_to_video.py` → `ImageToVideoConverter.convert_image_to_video()`

5. **转换完成**：
   - `core/image_to_video.py` → 返回转换结果
   - `utils/thread_utils.py` → 调用完成回调
   - `ui/main_window.py` → `MainWindow.on_conversion_complete()` → 更新UI显示

6. **配置保存**：
   - 用户修改参数时：`ui/main_window.py` → `config/settings.py` → `ConfigManager.set_setting()`
   - 程序关闭时：`ui/main_window.py` → `MainWindow.closeEvent()` → 保存窗口设置

### 命令行模式执行流程：

1. **程序启动**：
   - `main.py` → `run_command_line_mode()`

2. **转换执行**：
   - `main.py` → `core/image_to_video.py` → `ImageToVideoConverter()`
   - `main.py` → `ImageToVideoConverter.convert_image_to_video()`

3. **结果输出**：
   - `core/image_to_video.py` → 返回转换结果
   - `main.py` → 输出结果到控制台

## 外部依赖库

### 核心依赖：
- **opencv-python**: 图像和视频处理
- **Pillow**: 图像文件读取和验证
- **PyQt5**: GUI界面框架
- **numpy**: 数值计算支持

### 标准库依赖：
- **json**: 配置文件处理
- **os**: 文件系统操作
- **logging**: 日志记录
- **threading**: 多线程处理
- **queue**: 线程间通信
- **sys**: 系统相关操作
- **argparse**: 命令行参数解析
