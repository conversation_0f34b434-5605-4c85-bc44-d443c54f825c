# 项目架构说明

## 整体架构

图片转视频工具采用模块化设计，分为以下几个主要层次：

```
┌─────────────────────────────────────────┐
│                UI层                     │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ interface.py│  │ main_window.py  │   │
│  │   (界面)    │  │   (逻辑控制)    │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               业务逻辑层                 │
│  ┌─────────────────────────────────────┐ │
│  │     core/image_to_video.py          │ │
│  │        (核心转换逻辑)                │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               支撑服务层                 │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │config/      │  │ utils/          │   │
│  │settings.py  │  │ logging_config  │   │
│  │(配置管理)   │  │ thread_utils    │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

## 设计模式

### 1. MVC模式
- **Model**: `core/image_to_video.py` - 数据处理和业务逻辑
- **View**: `ui/interface.py` - 用户界面展示
- **Controller**: `ui/main_window.py` - 控制逻辑和事件处理

### 2. 单例模式
- `ConfigManager` 类确保配置的一致性

### 3. 观察者模式
- 使用PyQt信号槽机制实现组件间通信
- 进度回调函数实现转换进度的实时更新

### 4. 工厂模式
- `WorkerThread` 类用于创建后台工作线程

## 核心组件说明

### 1. 图片转视频核心引擎 (`core/image_to_video.py`)

**职责**：
- 图片文件验证和读取
- 视频编码和生成
- 进度跟踪和错误处理

**关键方法**：
- `validate_image_file()`: 验证图片文件有效性
- `convert_image_to_video()`: 执行图片到视频的转换
- `get_output_path()`: 生成输出文件路径

**设计特点**：
- 支持多种图片格式
- 可配置的视频参数（帧率、时长）
- 实时进度回调
- 完整的错误处理机制

### 2. 用户界面系统 (`ui/`)

**interface.py - 界面组件**：
- `DropArea`: 自定义拖拽区域控件
- `MainWindowInterface`: 主窗口界面布局管理

**main_window.py - 控制逻辑**：
- 事件处理和信号槽连接
- 多线程任务管理
- 配置数据绑定

**设计特点**：
- 响应式界面设计
- 拖拽文件支持
- 实时参数保存
- 多线程防止界面阻塞

### 3. 配置管理系统 (`config/`)

**职责**：
- 应用程序配置的持久化存储
- 默认配置管理
- 实时配置更新

**配置结构**：
```json
{
    "video_settings": {
        "default_duration": 5.0,
        "fps": 30,
        "quality": "high"
    },
    "ui_settings": {
        "window_width": 800,
        "window_height": 600,
        "theme": "default"
    },
    "processing_settings": {
        "enable_logging": true,
        "log_level": "INFO",
        "max_concurrent_tasks": 1
    }
}
```

### 4. 工具支撑系统 (`utils/`)

**logging_config.py - 日志系统**：
- 统一的日志配置
- 文件和控制台双重输出
- 可配置的日志级别

**thread_utils.py - 多线程工具**：
- `WorkerThread`: 后台任务执行
- `TaskQueue`: 任务队列管理
- 线程安全的回调机制

## 数据流向

### 1. 用户操作流
```
用户拖拽文件 → DropArea → files_dropped信号 → MainWindow.handle_files_dropped()
→ 文件验证 → 更新界面显示 → 等待用户操作
```

### 2. 转换处理流
```
用户点击转换 → MainWindow.start_conversion() → 创建WorkerThread
→ 后台执行convert_files() → 调用ImageToVideoConverter
→ 进度回调更新UI → 完成回调显示结果
```

### 3. 配置管理流
```
用户修改参数 → 信号触发 → MainWindow保存配置 → ConfigManager写入文件
程序启动 → ConfigManager读取配置 → MainWindow应用配置 → 界面显示
```

## 扩展性设计

### 1. 插件化支持
- 核心转换引擎与UI分离，便于添加新的转换算法
- 配置系统支持动态添加新的配置项

### 2. 多格式支持
- `ImageToVideoConverter` 设计为可扩展的格式支持
- 通过配置文件可以轻松添加新的图片和视频格式

### 3. 国际化准备
- 界面文本与逻辑分离
- 日志和错误消息统一管理

## 性能优化

### 1. 内存管理
- 大文件处理时的内存优化
- 及时释放图像资源

### 2. 并发处理
- 多线程避免UI阻塞
- 任务队列支持批量处理

### 3. 缓存机制
- 配置文件缓存减少IO操作
- 图片预处理结果缓存

## 错误处理策略

### 1. 分层错误处理
- 核心层：技术错误（文件读取、编码失败）
- 业务层：逻辑错误（参数验证、格式不支持）
- 界面层：用户友好的错误提示

### 2. 日志记录
- 详细的错误日志记录
- 用户操作日志跟踪
- 性能监控日志

### 3. 恢复机制
- 转换失败时的清理操作
- 配置文件损坏时的默认配置恢复
