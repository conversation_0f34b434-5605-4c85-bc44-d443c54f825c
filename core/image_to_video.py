"""
图片转视频核心功能模块
实现将单张图片转换为指定时长的视频文件
"""

import os
import cv2
import numpy as np
from PIL import Image
import logging


class ImageToVideoConverter:
    """图片转视频转换器类"""
    
    def __init__(self):
        """初始化转换器"""
        self.supported_image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        self.output_format = '.mp4'
        self.fps = 30  # 默认帧率
        
    def validate_image_file(self, image_path):
        """
        验证图片文件是否有效
        参数:
            image_path (str): 图片文件路径
        返回:
            bool: 文件是否有效
        """
        if not os.path.exists(image_path):
            logging.error(f"图片文件不存在: {image_path}")
            return False
            
        file_ext = os.path.splitext(image_path)[1].lower()
        if file_ext not in self.supported_image_formats:
            logging.error(f"不支持的图片格式: {file_ext}")
            return False
            
        try:
            # 尝试打开图片验证完整性
            with Image.open(image_path) as img:
                img.verify()
            return True
        except Exception as e:
            logging.error(f"图片文件损坏: {image_path}, 错误: {str(e)}")
            return False
    
    def get_output_path(self, image_path):
        """
        根据输入图片路径生成输出视频路径
        参数:
            image_path (str): 输入图片路径
        返回:
            str: 输出视频路径
        """
        dir_path = os.path.dirname(image_path)
        filename = os.path.splitext(os.path.basename(image_path))[0]
        output_path = os.path.join(dir_path, f"{filename}{self.output_format}")
        return output_path
    
    def convert_image_to_video(self, image_path, duration_seconds, progress_callback=None):
        """
        将图片转换为视频
        参数:
            image_path (str): 输入图片路径
            duration_seconds (float): 视频时长（秒）
            progress_callback (function): 进度回调函数，接收进度百分比(0-100)
        返回:
            tuple: (是否成功, 输出路径或错误信息)
        """
        try:
            # 验证输入文件
            if not self.validate_image_file(image_path):
                return False, "图片文件无效"
            
            # 生成输出路径
            output_path = self.get_output_path(image_path)
            
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                return False, "无法读取图片文件"
            
            height, width, layers = image.shape
            
            # 计算总帧数
            total_frames = int(duration_seconds * self.fps)
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_path, fourcc, self.fps, (width, height))
            
            if not video_writer.isOpened():
                return False, "无法创建视频文件"
            
            # 写入帧
            for frame_num in range(total_frames):
                video_writer.write(image)
                
                # 更新进度
                if progress_callback:
                    progress = int((frame_num + 1) / total_frames * 100)
                    progress_callback(progress)
            
            # 释放资源
            video_writer.release()
            
            # 验证输出文件
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                logging.info(f"视频转换成功: {output_path}")
                return True, output_path
            else:
                return False, "视频文件创建失败"
                
        except Exception as e:
            error_msg = f"转换过程中发生错误: {str(e)}"
            logging.error(error_msg)
            return False, error_msg
    
    def set_fps(self, fps):
        """
        设置视频帧率
        参数:
            fps (int): 帧率值，范围1-60
        """
        if 1 <= fps <= 60:
            self.fps = fps
        else:
            logging.warning(f"无效的帧率值: {fps}，使用默认值30")
            self.fps = 30
    
    def get_supported_formats(self):
        """
        获取支持的图片格式列表
        返回:
            list: 支持的图片格式扩展名列表
        """
        return self.supported_image_formats.copy()
