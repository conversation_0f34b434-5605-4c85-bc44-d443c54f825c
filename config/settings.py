"""
配置管理模块
负责读取和保存应用程序配置
"""

import json
import os
import logging
from typing import Dict, Any


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file_path=None):
        """
        初始化配置管理器
        参数:
            config_file_path (str): 配置文件路径，默认为当前目录下的config.json
        """
        if config_file_path is None:
            # 使用绝对路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.config_file_path = os.path.join(current_dir, 'config.json')
        else:
            self.config_file_path = config_file_path
            
        self.config_data = {}
        self.default_config = {
            "video_settings": {
                "default_duration": 5.0,
                "fps": 30,
                "quality": "high",
                "output_dir": "",
                "use_custom_output_dir": False
            },
            "ui_settings": {
                "window_width": 800,
                "window_height": 600,
                "theme": "default"
            },
            "processing_settings": {
                "enable_logging": True,
                "log_level": "INFO",
                "max_concurrent_tasks": 1
            }
        }
        
        # 加载配置
        self.load_config()
    
    def load_config(self):
        """
        从配置文件加载配置数据
        如果文件不存在或损坏，使用默认配置
        """
        try:
            if os.path.exists(self.config_file_path):
                with open(self.config_file_path, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                logging.info(f"配置文件加载成功: {self.config_file_path}")
            else:
                logging.info("配置文件不存在，使用默认配置")
                self.config_data = self.default_config.copy()
                self.save_config()
        except Exception as e:
            logging.error(f"加载配置文件失败: {str(e)}，使用默认配置")
            self.config_data = self.default_config.copy()
            self.save_config()
    
    def save_config(self):
        """
        保存配置数据到文件
        返回:
            bool: 保存是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file_path), exist_ok=True)
            
            with open(self.config_file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=4, ensure_ascii=False)
            logging.info(f"配置文件保存成功: {self.config_file_path}")
            return True
        except Exception as e:
            logging.error(f"保存配置文件失败: {str(e)}")
            return False
    
    def get_setting(self, section, key, default_value=None):
        """
        获取配置项的值
        参数:
            section (str): 配置节名称
            key (str): 配置项键名
            default_value: 默认值
        返回:
            配置项的值或默认值
        """
        try:
            return self.config_data.get(section, {}).get(key, default_value)
        except Exception as e:
            logging.error(f"获取配置项失败: {section}.{key}, 错误: {str(e)}")
            return default_value
    
    def set_setting(self, section, key, value):
        """
        设置配置项的值
        参数:
            section (str): 配置节名称
            key (str): 配置项键名
            value: 配置项的值
        返回:
            bool: 设置是否成功
        """
        try:
            if section not in self.config_data:
                self.config_data[section] = {}
            
            self.config_data[section][key] = value
            return self.save_config()
        except Exception as e:
            logging.error(f"设置配置项失败: {section}.{key}, 错误: {str(e)}")
            return False
    
    def get_video_settings(self):
        """
        获取视频相关设置
        返回:
            dict: 视频设置字典
        """
        return self.config_data.get('video_settings', self.default_config['video_settings'])
    
    def get_ui_settings(self):
        """
        获取UI相关设置
        返回:
            dict: UI设置字典
        """
        return self.config_data.get('ui_settings', self.default_config['ui_settings'])
    
    def get_processing_settings(self):
        """
        获取处理相关设置
        返回:
            dict: 处理设置字典
        """
        return self.config_data.get('processing_settings', self.default_config['processing_settings'])
    
    def reset_to_default(self):
        """
        重置配置为默认值
        返回:
            bool: 重置是否成功
        """
        try:
            self.config_data = self.default_config.copy()
            return self.save_config()
        except Exception as e:
            logging.error(f"重置配置失败: {str(e)}")
            return False
