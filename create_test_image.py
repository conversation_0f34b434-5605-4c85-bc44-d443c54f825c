#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试图片
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_image():
    """创建一个简单的测试图片"""
    # 创建图片
    image = Image.new('RGB', (800, 600), color='lightblue')
    draw = ImageDraw.Draw(image)
    
    # 绘制一些简单的图形
    # 圆形
    draw.ellipse([100, 100, 300, 300], fill='red', outline='darkred', width=3)
    
    # 矩形
    draw.rectangle([400, 100, 600, 300], fill='green', outline='darkgreen', width=3)
    
    # 添加文字
    try:
        font = ImageFont.truetype("arial.ttf", 40)
    except:
        font = ImageFont.load_default()
    
    text = "Test Image"
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_x = (800 - text_width) // 2
    text_y = 450
    
    draw.text((text_x, text_y), text, fill='black', font=font)
    
    # 保存图片
    filename = "test_image.jpg"
    image.save(filename, 'JPEG', quality=95)
    print(f"测试图片已创建: {filename}")
    return filename

if __name__ == '__main__':
    create_test_image()
